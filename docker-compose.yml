services:
  db:
    image: postgres:16-alpine
    restart: always
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "5440:5432"
    volumes:
      - db_data:/var/lib/postgresql/data

  app:
    build: .
    restart: always
    ports:
      - "5173:5173"
      - "5001:5000"
    env_file:
      - ./.env
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      - PYTHON_RAGFLOW_SERVICE_URL=http://python-ragflow-service:8001
    depends_on:
      - db
      - python-ragflow-service

  python-ragflow-service:
    build: ./python-ragflow-service
    restart: always
    ports:
      - "8001:8001"
    volumes:
      - ./ragflow:/app/ragflow:ro  # Mount RAGFlow SDK as read-only
    environment:
      - LOG_LEVEL=INFO
      - CORS_ORIGINS=*
      - CORS_CREDENTIALS=true

volumes:
  db_data: 