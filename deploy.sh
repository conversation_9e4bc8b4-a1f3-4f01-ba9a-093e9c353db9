#!/bin/bash

# JURBOT with Enhanced RAGFlow Service - Automated Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="JURBOT Enhanced RAGFlow"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
PYTHON_ENV_FILE="python-ragflow-service/.env"

# Function to print colored output
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}🚀 $1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    print_success "Docker found: $(docker --version)"
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    print_success "Docker Compose found: $(docker-compose --version)"
    
    # Check if we're in the right directory
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        print_error "docker-compose.yml not found. Please run this script from the JURBOT root directory."
        exit 1
    fi
    print_success "Found docker-compose.yml"
    
    # Check if RAGFlow directory exists
    if [[ ! -d "ragflow" ]]; then
        print_error "RAGFlow directory not found. Please ensure the RAGFlow repository is available."
        print_info "Expected location: $(pwd)/ragflow"
        exit 1
    fi
    print_success "Found RAGFlow directory"
    
    # Check if Python service directory exists
    if [[ ! -d "python-ragflow-service" ]]; then
        print_error "Python RAGFlow service directory not found."
        exit 1
    fi
    print_success "Found Python RAGFlow service directory"
}

# Function to setup environment files
setup_environment() {
    print_header "Setting Up Environment"
    
    # Setup main .env file
    if [[ ! -f "$ENV_FILE" ]]; then
        if [[ -f ".env.example" ]]; then
            cp .env.example "$ENV_FILE"
            print_success "Created $ENV_FILE from .env.example"
        else
            print_warning "No .env file found. Creating basic configuration..."
            cat > "$ENV_FILE" << EOF
# JURBOT Environment Configuration
POSTGRES_USER=jurbot
POSTGRES_PASSWORD=jurbot_password
POSTGRES_DB=jurbot

# Add your other environment variables here
EOF
            print_success "Created basic $ENV_FILE"
        fi
    else
        print_success "Found existing $ENV_FILE"
    fi
    
    # Setup Python service .env file
    if [[ ! -f "$PYTHON_ENV_FILE" ]]; then
        if [[ -f "python-ragflow-service/.env.example" ]]; then
            cp python-ragflow-service/.env.example "$PYTHON_ENV_FILE"
            print_success "Created $PYTHON_ENV_FILE from .env.example"
        else
            print_warning "No Python service .env.example found. Creating basic configuration..."
            cat > "$PYTHON_ENV_FILE" << EOF
# Python RAGFlow Service Environment Configuration
PYTHON_RAGFLOW_SERVICE_PORT=8001
PYTHON_RAGFLOW_SERVICE_HOST=0.0.0.0
LOG_LEVEL=INFO
CORS_ORIGINS=*
CORS_CREDENTIALS=true

# RAGFlow Configuration (configure these)
# RAGFLOW_API_KEY=your_ragflow_api_key_here
# RAGFLOW_BASE_URL=http://**************
# RAGFLOW_CHAT_ID=your_chat_id_here
EOF
            print_success "Created basic $PYTHON_ENV_FILE"
        fi
    else
        print_success "Found existing $PYTHON_ENV_FILE"
    fi
    
    # Check if RAGFlow configuration is set
    if ! grep -q "RAGFLOW_API_KEY=" "$PYTHON_ENV_FILE" || grep -q "your_ragflow_api_key_here" "$PYTHON_ENV_FILE"; then
        print_warning "RAGFlow configuration not set in $PYTHON_ENV_FILE"
        print_info "Please configure your RAGFlow API key, base URL, and chat ID"
    fi
}

# Function to create necessary directories
create_directories() {
    print_header "Creating Directories"
    
    # Create logs directory for Python service
    mkdir -p python-ragflow-service/logs
    print_success "Created python-ragflow-service/logs directory"
    
    # Set proper permissions
    chmod 755 python-ragflow-service/logs
    print_success "Set permissions for logs directory"
}

# Function to build and start services
deploy_services() {
    print_header "Building and Starting Services"
    
    # Stop any existing services
    print_info "Stopping existing services..."
    docker-compose down --remove-orphans || true
    
    # Build services
    print_info "Building services..."
    docker-compose build --no-cache
    
    # Start services
    print_info "Starting services..."
    docker-compose up -d
    
    print_success "Services started successfully"
}

# Function to wait for services to be ready
wait_for_services() {
    print_header "Waiting for Services to be Ready"
    
    # Wait for database
    print_info "Waiting for database to be ready..."
    timeout=60
    while ! docker-compose exec -T db pg_isready -U ${POSTGRES_USER:-jurbot} &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            print_error "Database failed to start within 60 seconds"
            exit 1
        fi
    done
    print_success "Database is ready"
    
    # Wait for Python RAGFlow service
    print_info "Waiting for Python RAGFlow service to be ready..."
    timeout=60
    while ! curl -s -f http://localhost:8001/health/simple &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            print_error "Python RAGFlow service failed to start within 60 seconds"
            print_info "Check logs with: docker-compose logs python-ragflow-service"
            exit 1
        fi
    done
    print_success "Python RAGFlow service is ready"
    
    # Wait for main application
    print_info "Waiting for main application to be ready..."
    timeout=60
    while ! curl -s -f http://localhost:5001/health &>/dev/null && ! curl -s -f http://localhost:5173 &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            print_warning "Main application may not be fully ready yet"
            break
        fi
    done
    print_success "Main application is ready"
}

# Function to run health checks
run_health_checks() {
    print_header "Running Health Checks"
    
    # Check Python RAGFlow service health
    print_info "Checking Python RAGFlow service health..."
    if curl -s -f http://localhost:8001/health | jq -e '.status == "healthy"' &>/dev/null; then
        print_success "Python RAGFlow service is healthy"
    else
        print_warning "Python RAGFlow service health check failed"
        print_info "Check detailed health: curl http://localhost:8001/health | jq"
    fi
    
    # Check if RAGFlow SDK is available
    print_info "Checking RAGFlow SDK availability..."
    if docker-compose exec -T python-ragflow-service python -c "import sys; sys.path.insert(0, '/app/ragflow/sdk/python'); import ragflow_sdk" &>/dev/null; then
        print_success "RAGFlow SDK is available"
    else
        print_warning "RAGFlow SDK import failed"
        print_info "Check if RAGFlow directory is properly mounted"
    fi
}

# Function to show deployment summary
show_summary() {
    print_header "Deployment Summary"
    
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📊 Service URLs:${NC}"
    echo -e "  • Main Application: ${GREEN}http://localhost:5173${NC}"
    echo -e "  • Backend API: ${GREEN}http://localhost:5001${NC}"
    echo -e "  • Python RAGFlow Service: ${GREEN}http://localhost:8001${NC}"
    echo -e "  • Database: ${GREEN}localhost:5440${NC}"
    echo ""
    echo -e "${BLUE}🔍 Health Checks:${NC}"
    echo -e "  • Python Service Health: ${GREEN}curl http://localhost:8001/health${NC}"
    echo -e "  • Python Service Simple: ${GREEN}curl http://localhost:8001/health/simple${NC}"
    echo ""
    echo -e "${BLUE}📋 Useful Commands:${NC}"
    echo -e "  • View logs: ${YELLOW}docker-compose logs -f${NC}"
    echo -e "  • View Python service logs: ${YELLOW}docker-compose logs -f python-ragflow-service${NC}"
    echo -e "  • Stop services: ${YELLOW}docker-compose down${NC}"
    echo -e "  • Restart services: ${YELLOW}docker-compose restart${NC}"
    echo ""
    echo -e "${BLUE}🧪 Testing:${NC}"
    echo -e "  • Run debug check: ${YELLOW}python python-ragflow-service/debug.py${NC}"
    echo -e "  • Run tests: ${YELLOW}python python-ragflow-service/test_client.py --test all --ragflow-api-key YOUR_KEY --ragflow-base-url YOUR_URL --ragflow-chat-id YOUR_CHAT_ID${NC}"
    echo ""
    
    if grep -q "your_ragflow_api_key_here" "$PYTHON_ENV_FILE" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  Don't forget to configure your RAGFlow settings in:${NC}"
        echo -e "  • ${YELLOW}$PYTHON_ENV_FILE${NC}"
        echo -e "  • JURBOT frontend settings${NC}"
    fi
}

# Function to handle cleanup on error
cleanup_on_error() {
    print_error "Deployment failed. Cleaning up..."
    docker-compose down --remove-orphans || true
    exit 1
}

# Main deployment function
main() {
    # Set up error handling
    trap cleanup_on_error ERR
    
    print_header "$PROJECT_NAME - Automated Deployment"
    
    # Run deployment steps
    check_prerequisites
    setup_environment
    create_directories
    deploy_services
    wait_for_services
    run_health_checks
    show_summary
    
    print_success "Deployment completed successfully! 🎉"
}

# Parse command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --logs         Show logs after deployment"
        echo "  --no-wait      Skip waiting for services to be ready"
        echo ""
        echo "This script will:"
        echo "  1. Check prerequisites (Docker, Docker Compose)"
        echo "  2. Setup environment files"
        echo "  3. Create necessary directories"
        echo "  4. Build and start all services"
        echo "  5. Wait for services to be ready"
        echo "  6. Run health checks"
        echo ""
        exit 0
        ;;
    --logs)
        main
        echo ""
        print_info "Showing logs (Ctrl+C to exit)..."
        docker-compose logs -f
        ;;
    --no-wait)
        # Override wait function to do nothing
        wait_for_services() {
            print_info "Skipping service readiness checks"
        }
        main
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
