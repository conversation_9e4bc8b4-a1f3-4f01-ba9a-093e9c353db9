# Enhanced RAGFlow Service - Deployment Guide

This guide covers deploying the Enhanced RAGFlow Service for the JURBOT application with citations, thinking process, and document references.

## 🚀 Quick Start

### Prerequisites

1. **Docker and Docker Compose** installed
2. **RAGFlow instance** running and accessible
3. **RAGFlow repository** cloned locally
4. **API credentials** for your RAGFlow instance

### Basic Deployment

1. **Setup configuration:**
   ```bash
   cd /home/<USER>/jurbot

   # Copy environment configuration
   cp python-ragflow-service/.env.example python-ragflow-service/.env
   ```

2. **Configure environment:**
   ```bash
   # Edit the environment file
   nano python-ragflow-service/.env
   
   # Set your RAGFlow configuration
   RAGFLOW_API_KEY=your_api_key_here
   RAGFLOW_BASE_URL=http://**************
   RAGFLOW_CHAT_ID=your_chat_id_here
   ```

3. **Start the services:**
   ```bash
   docker-compose up -d
   ```

4. **Verify deployment:**
   ```bash
   # Check service health
   curl http://localhost:8001/health
   
   # Run debug check
   python python-ragflow-service/debug.py
   ```

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Node.js        │    │   Python        │
│   (React)       │◄──►│   Backend        │◄──►│   RAGFlow       │
│                 │    │   (Express)      │    │   Service       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
                                               ┌─────────────────┐
                                               │   RAGFlow       │
                                               │   Python SDK    │
                                               └─────────────────┘
                                                         │
                                                         ▼
                                               ┌─────────────────┐
                                               │   RAGFlow       │
                                               │   API Server    │
                                               └─────────────────┘
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `PYTHON_RAGFLOW_SERVICE_PORT` | 8001 | Service port |
| `PYTHON_RAGFLOW_SERVICE_HOST` | 0.0.0.0 | Service host |
| `LOG_LEVEL` | INFO | Logging level |
| `REDIS_URL` | None | Redis URL for session storage |
| `CORS_ORIGINS` | * | CORS allowed origins |
| `WORKERS` | 1 | Number of worker processes |

### RAGFlow Configuration

Configure in the JURBOT frontend settings:

1. **Engine**: Select "RAGFlow"
2. **API Key**: Your RAGFlow API key
3. **Base URL**: Your RAGFlow server URL
4. **Chat ID**: Your RAGFlow assistant/chat ID

## 🐳 Docker Deployment

### Development

```bash
# Start with development configuration
docker-compose up -d

# View logs
docker-compose logs -f python-ragflow-service

# Stop services
docker-compose down
```

### Production

```bash
# Use production configuration
docker-compose -f python-ragflow-service/docker-compose.prod.yml up -d

# With Redis session storage
REDIS_URL=redis://redis:6379/0 docker-compose -f python-ragflow-service/docker-compose.prod.yml up -d

# With monitoring (Prometheus + Grafana)
docker-compose -f python-ragflow-service/docker-compose.prod.yml --profile monitoring up -d
```

## 🔍 Testing and Validation

### Health Checks

```bash
# Basic health check
curl http://localhost:8001/health/simple

# Detailed health check
curl http://localhost:8001/health | jq

# Debug check
python python-ragflow-service/debug.py
```

### Functional Testing

```bash
# Run comprehensive tests
python python-ragflow-service/test_client.py \
  --ragflow-api-key "your_key" \
  --ragflow-base-url "http://ragflow-server" \
  --ragflow-chat-id "your_chat_id"

# Test specific functionality
python python-ragflow-service/test_client.py --test stream \
  --ragflow-api-key "your_key" \
  --ragflow-base-url "http://ragflow-server" \
  --ragflow-chat-id "your_chat_id"
```

### Monitoring

```bash
# Monitor service continuously
python python-ragflow-service/monitor.py --continuous

# Single check with chat testing
python python-ragflow-service/monitor.py \
  --test-chat \
  --ragflow-api-key "your_key" \
  --ragflow-base-url "http://ragflow-server" \
  --ragflow-chat-id "your_chat_id"
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Service Not Starting

**Symptoms:**
- Service fails to start
- Health check returns connection refused

**Solutions:**
```bash
# Check logs
docker-compose logs python-ragflow-service

# Run debug script
python python-ragflow-service/debug.py

# Check port availability
lsof -i :8001
```

#### 2. RAGFlow SDK Import Errors

**Symptoms:**
- Import errors in logs
- "RAGFlow SDK not found" in health check

**Solutions:**
```bash
# Ensure RAGFlow repo is mounted correctly
ls -la ragflow/sdk/python/

# Check Docker volume mount
docker-compose exec python-ragflow-service ls -la /app/ragflow/sdk/python/

# Verify Python path
docker-compose exec python-ragflow-service python -c "import sys; print(sys.path)"
```

#### 3. Citation Markers Not Working

**Symptoms:**
- Citations not appearing as interactive elements
- `[ID:n]` markers showing as plain text

**Solutions:**
```bash
# Check if RAGFlow is returning citation markers
curl -X POST http://localhost:8001/chat/complete \
  -H "Content-Type: application/json" \
  -d '{"message": "test", "session_id": "test", "ragflow_config": {...}}'

# Verify frontend is using enhanced components
# Check browser console for errors
```

#### 4. Session Storage Issues

**Symptoms:**
- Sessions not persisting
- "Session storage error" in health check

**Solutions:**
```bash
# Check Redis connection (if using Redis)
docker-compose exec redis redis-cli ping

# Check session storage in health endpoint
curl http://localhost:8001/health | jq '.sessions'

# Test session storage directly
python -c "
import asyncio
from python-ragflow-service.session_storage import get_session_storage
async def test():
    storage = await get_session_storage()
    await storage.set('test', 'value')
    print(await storage.get('test'))
asyncio.run(test())
"
```

### Performance Issues

#### High Memory Usage

```bash
# Check memory usage
docker stats python-ragflow-service

# Reduce workers in production
WORKERS=2 docker-compose up -d

# Enable memory limits
docker-compose -f python-ragflow-service/docker-compose.prod.yml up -d
```

#### Slow Response Times

```bash
# Check performance logs
tail -f python-ragflow-service/logs/ragflow_performance.log

# Monitor with continuous health checks
python python-ragflow-service/monitor.py --continuous --interval 10

# Check RAGFlow server response times
curl -w "@curl-format.txt" -o /dev/null -s http://ragflow-server/api/health
```

## 📊 Monitoring and Observability

### Logs

```bash
# Service logs
docker-compose logs -f python-ragflow-service

# Error logs only
tail -f python-ragflow-service/logs/ragflow_errors.log

# Performance logs
tail -f python-ragflow-service/logs/ragflow_performance.log
```

### Metrics

With Prometheus monitoring enabled:

- **Service Health**: `http://localhost:9090`
- **Grafana Dashboard**: `http://localhost:3000`
- **Default Credentials**: admin/admin

### Health Endpoints

- **Simple**: `GET /health/simple` - Basic OK/ERROR
- **Detailed**: `GET /health` - Comprehensive health info
- **Metrics**: Available via Prometheus scraping

## 🔒 Security Considerations

### Production Security

1. **Environment Variables:**
   ```bash
   # Use secrets management
   export RAGFLOW_API_KEY=$(cat /run/secrets/ragflow_api_key)
   ```

2. **CORS Configuration:**
   ```bash
   # Restrict CORS origins
   CORS_ORIGINS=https://yourdomain.com,https://api.yourdomain.com
   ```

3. **Redis Security:**
   ```bash
   # Enable Redis authentication
   echo "requirepass your_secure_password" >> redis.conf
   REDIS_URL=redis://:your_secure_password@redis:6379/0
   ```

4. **Network Security:**
   ```bash
   # Use internal networks
   docker network create ragflow_internal
   ```

## 📈 Scaling

### Horizontal Scaling

```bash
# Scale service instances
docker-compose up -d --scale python-ragflow-service=3

# Use load balancer (nginx)
docker-compose -f python-ragflow-service/docker-compose.prod.yml --profile with-nginx up -d
```

### Vertical Scaling

```bash
# Increase workers
WORKERS=4 docker-compose up -d

# Increase memory limits
# Edit docker-compose.prod.yml memory limits
```

## 🔄 Updates and Maintenance

### Updating the Service

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose build python-ragflow-service
docker-compose up -d python-ragflow-service

# Verify update
curl http://localhost:8001/health | jq '.version'
```

### Backup and Recovery

```bash
# Backup Redis data (if using Redis)
docker-compose exec redis redis-cli BGSAVE

# Backup logs
tar -czf logs-backup-$(date +%Y%m%d).tar.gz python-ragflow-service/logs/

# Restore Redis data
docker-compose down
# Copy backup to redis volume
docker-compose up -d
```

For additional support, check the service logs and use the debug utilities provided.
