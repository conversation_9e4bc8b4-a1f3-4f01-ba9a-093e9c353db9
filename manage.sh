#!/bin/bash

# JURBOT Enhanced RAGFlow Service - Management Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.yml"
PROD_COMPOSE_FILE="docker-compose.prod.yml"

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to show help
show_help() {
    echo "JURBOT Enhanced RAGFlow Service - Management Script"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  start [dev|prod]     Start services (default: dev)"
    echo "  stop [dev|prod]      Stop services (default: dev)"
    echo "  restart [dev|prod]   Restart services (default: dev)"
    echo "  logs [service]       Show logs (all services or specific service)"
    echo "  status               Show service status"
    echo "  health               Check service health"
    echo "  test                 Run service tests"
    echo "  debug                Run debug checks"
    echo "  monitor              Start continuous monitoring"
    echo "  backup               Backup data"
    echo "  clean                Clean up containers and volumes"
    echo "  update               Update and rebuild services"
    echo ""
    echo "Examples:"
    echo "  $0 start dev         Start development environment"
    echo "  $0 start prod        Start production environment"
    echo "  $0 logs python-ragflow-service"
    echo "  $0 test              Run comprehensive tests"
    echo "  $0 health            Check all service health"
}

# Function to determine compose file
get_compose_file() {
    local mode=${1:-dev}
    if [[ "$mode" == "prod" ]]; then
        echo "$PROD_COMPOSE_FILE"
    else
        echo "$COMPOSE_FILE"
    fi
}

# Function to start services
start_services() {
    local mode=${1:-dev}
    local compose_file=$(get_compose_file "$mode")
    
    print_info "Starting services in $mode mode..."
    
    if [[ "$mode" == "prod" ]]; then
        if [[ ! -f "$compose_file" ]]; then
            print_error "Production compose file not found. Run deploy-production.sh first."
            exit 1
        fi
        docker-compose -f "$compose_file" up -d
    else
        docker-compose up -d
    fi
    
    print_success "Services started in $mode mode"
}

# Function to stop services
stop_services() {
    local mode=${1:-dev}
    local compose_file=$(get_compose_file "$mode")
    
    print_info "Stopping services in $mode mode..."
    
    if [[ "$mode" == "prod" && -f "$compose_file" ]]; then
        docker-compose -f "$compose_file" down
    else
        docker-compose down
    fi
    
    print_success "Services stopped"
}

# Function to restart services
restart_services() {
    local mode=${1:-dev}
    
    print_info "Restarting services in $mode mode..."
    stop_services "$mode"
    sleep 2
    start_services "$mode"
    print_success "Services restarted"
}

# Function to show logs
show_logs() {
    local service=${1:-}
    
    if [[ -n "$service" ]]; then
        print_info "Showing logs for $service..."
        docker-compose logs -f "$service"
    else
        print_info "Showing logs for all services..."
        docker-compose logs -f
    fi
}

# Function to show status
show_status() {
    print_info "Service Status:"
    echo ""
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        docker-compose ps
    else
        print_warning "No services are currently running"
    fi
    
    echo ""
    print_info "Docker containers:"
    docker ps --filter "name=jurbot" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# Function to check health
check_health() {
    print_info "Checking service health..."
    echo ""
    
    # Check Python RAGFlow service
    if curl -s -f http://localhost:8001/health/simple &>/dev/null; then
        print_success "Python RAGFlow service: Healthy"
        
        # Get detailed health info
        if command -v jq &>/dev/null; then
            health_data=$(curl -s http://localhost:8001/health)
            status=$(echo "$health_data" | jq -r '.status')
            storage_type=$(echo "$health_data" | jq -r '.sessions.storage_type // "unknown"')
            active_sessions=$(echo "$health_data" | jq -r '.sessions.active_sessions // 0')
            
            echo "  Status: $status"
            echo "  Session storage: $storage_type"
            echo "  Active sessions: $active_sessions"
        fi
    else
        print_error "Python RAGFlow service: Not responding"
    fi
    
    # Check main application
    if curl -s -f http://localhost:5173 &>/dev/null || curl -s -f http://localhost:5001/health &>/dev/null; then
        print_success "Main application: Healthy"
    else
        print_error "Main application: Not responding"
    fi
    
    # Check database
    if docker-compose exec -T db pg_isready -U ${POSTGRES_USER:-jurbot} &>/dev/null; then
        print_success "Database: Healthy"
    else
        print_error "Database: Not responding"
    fi
    
    # Check Redis (if running)
    if docker-compose ps | grep -q redis; then
        if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
            print_success "Redis: Healthy"
        else
            print_error "Redis: Not responding"
        fi
    fi
}

# Function to run tests
run_tests() {
    print_info "Running service tests..."
    
    # Check if Python service is running
    if ! curl -s -f http://localhost:8001/health/simple &>/dev/null; then
        print_error "Python RAGFlow service is not running. Start services first."
        exit 1
    fi
    
    # Run debug check
    print_info "Running debug check..."
    python python-ragflow-service/debug.py
    
    echo ""
    print_info "To run comprehensive tests with RAGFlow, use:"
    echo "python python-ragflow-service/test_client.py --test all \\"
    echo "  --ragflow-api-key YOUR_API_KEY \\"
    echo "  --ragflow-base-url YOUR_BASE_URL \\"
    echo "  --ragflow-chat-id YOUR_CHAT_ID"
}

# Function to run debug
run_debug() {
    print_info "Running debug checks..."
    python python-ragflow-service/debug.py
}

# Function to start monitoring
start_monitoring() {
    print_info "Starting continuous monitoring..."
    python python-ragflow-service/monitor.py --continuous
}

# Function to backup data
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    
    print_info "Creating backup in $backup_dir..."
    mkdir -p "$backup_dir"
    
    # Backup database
    if docker-compose ps | grep -q db; then
        print_info "Backing up database..."
        docker-compose exec -T db pg_dump -U ${POSTGRES_USER:-jurbot} ${POSTGRES_DB:-jurbot} > "$backup_dir/database.sql"
        print_success "Database backed up"
    fi
    
    # Backup Redis (if running)
    if docker-compose ps | grep -q redis; then
        print_info "Backing up Redis..."
        docker-compose exec -T redis redis-cli BGSAVE
        docker cp $(docker-compose ps -q redis):/data/dump.rdb "$backup_dir/redis.rdb"
        print_success "Redis backed up"
    fi
    
    # Backup logs
    if [[ -d "python-ragflow-service/logs" ]]; then
        print_info "Backing up logs..."
        cp -r python-ragflow-service/logs "$backup_dir/"
        print_success "Logs backed up"
    fi
    
    # Backup configuration
    print_info "Backing up configuration..."
    cp .env "$backup_dir/" 2>/dev/null || true
    cp python-ragflow-service/.env "$backup_dir/python-service.env" 2>/dev/null || true
    
    print_success "Backup completed: $backup_dir"
}

# Function to clean up
clean_up() {
    print_warning "This will remove all containers, networks, and volumes. Are you sure? (y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        print_info "Cleaning up..."
        
        # Stop and remove containers
        docker-compose down --remove-orphans --volumes
        
        # Remove production containers if they exist
        if [[ -f "$PROD_COMPOSE_FILE" ]]; then
            docker-compose -f "$PROD_COMPOSE_FILE" down --remove-orphans --volumes 2>/dev/null || true
        fi
        
        # Remove unused images
        docker image prune -f
        
        # Remove unused volumes
        docker volume prune -f
        
        print_success "Cleanup completed"
    else
        print_info "Cleanup cancelled"
    fi
}

# Function to update services
update_services() {
    local mode=${1:-dev}
    
    print_info "Updating services in $mode mode..."
    
    # Pull latest code (if in git repo)
    if [[ -d ".git" ]]; then
        print_info "Pulling latest code..."
        git pull
    fi
    
    # Rebuild and restart services
    print_info "Rebuilding services..."
    if [[ "$mode" == "prod" ]]; then
        docker-compose -f "$PROD_COMPOSE_FILE" build --no-cache
        docker-compose -f "$PROD_COMPOSE_FILE" up -d
    else
        docker-compose build --no-cache
        docker-compose up -d
    fi
    
    print_success "Services updated and restarted"
}

# Main script logic
case "${1:-}" in
    start)
        start_services "${2:-dev}"
        ;;
    stop)
        stop_services "${2:-dev}"
        ;;
    restart)
        restart_services "${2:-dev}"
        ;;
    logs)
        show_logs "${2:-}"
        ;;
    status)
        show_status
        ;;
    health)
        check_health
        ;;
    test)
        run_tests
        ;;
    debug)
        run_debug
        ;;
    monitor)
        start_monitoring
        ;;
    backup)
        backup_data
        ;;
    clean)
        clean_up
        ;;
    update)
        update_services "${2:-dev}"
        ;;
    --help|-h|help)
        show_help
        ;;
    "")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
