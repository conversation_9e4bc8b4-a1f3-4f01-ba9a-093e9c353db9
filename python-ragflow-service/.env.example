# Python RAGFlow Service Environment Variables

# Service Configuration
PYTHON_RAGFLOW_SERVICE_PORT=8001
PYTHON_RAGFLOW_SERVICE_HOST=0.0.0.0

# RAGFlow Configuration (these can be overridden by request headers)
RAGFLOW_API_KEY=your_ragflow_api_key_here
RAGFLOW_BASE_URL=http://**************
RAGFLOW_CHAT_ID=your_chat_id_here

# Logging
LOG_LEVEL=INFO

# CORS Configuration
CORS_ORIGINS=*
CORS_CREDENTIALS=true
