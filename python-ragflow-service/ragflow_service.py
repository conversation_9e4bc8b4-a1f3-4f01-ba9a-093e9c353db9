import os
import json
import re
import logging
import asyncio
from typing import Dict, Optional, List, Any, Iterator
import httpx

logger = logging.getLogger(__name__)

class RAGFlowEnhancedService:
    """
    Enhanced RAGFlow service that provides citations, thinking process, and document references
    Uses RAGFlow HTTP API directly instead of Python SDK
    """

    def __init__(self):
        self.http_client = httpx.AsyncClient(timeout=30.0)
        self.session_mappings: Dict[str, str] = {}  # Maps our session IDs to RAGFlow session IDs

    async def close(self):
        """Close HTTP client"""
        await self.http_client.aclose()

    async def call_ragflow_api(
        self,
        api_key: str,
        base_url: str,
        chat_id: str,
        message: str,
        session_id: Optional[str] = None,
        stream: bool = True
    ) -> httpx.Response:
        """Call RAGFlow API directly"""
        url = f"{base_url}/api/v1/chats/{chat_id}/completions"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        payload = {
            "question": message,
            "stream": stream
        }

        # Use RAGFlow session ID if we have a mapping
        if session_id and session_id in self.session_mappings:
            payload["session_id"] = self.session_mappings[session_id]

        logger.info(f"Calling RAGFlow API: {url}")
        logger.debug(f"Payload: {payload}")

        response = await self.http_client.post(url, json=payload, headers=headers)

        if not response.is_success:
            logger.error(f"RAGFlow API error: {response.status_code} - {response.text}")
            raise Exception(f"RAGFlow API error: {response.status_code} - {response.text}")

        return response
    
    def process_content_for_citations(self, content: str) -> Dict[str, Any]:
        """
        Process content to extract thinking process and prepare for citation handling
        """
        result = {
            "original_content": content,
            "processed_content": content,
            "thinking_process": None,
            "citation_markers": []
        }
        
        # Extract thinking process
        think_pattern = r'<think>(.*?)</think>'
        think_matches = re.findall(think_pattern, content, re.DOTALL)
        
        if think_matches:
            result["thinking_process"] = think_matches[0].strip()
            # Remove thinking tags from main content but keep for display
            result["processed_content"] = re.sub(think_pattern, '', content, flags=re.DOTALL).strip()
        
        # Find citation markers [ID:n]
        citation_pattern = r'\[ID:(\d+)\]'
        citation_matches = re.findall(citation_pattern, content)
        result["citation_markers"] = [int(match) for match in citation_matches]
        
        return result
    
    def format_references_for_frontend(self, reference: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format RAGFlow references for frontend consumption
        """
        if not reference:
            return {}
        
        formatted = {
            "chunks": reference.get("chunks", []),
            "doc_aggs": reference.get("doc_aggs", []),
            "total": reference.get("total", 0)
        }
        
        # Ensure chunks have proper structure for frontend
        for i, chunk in enumerate(formatted["chunks"]):
            if isinstance(chunk, dict):
                # Add index for citation mapping
                chunk["index"] = i
                # Ensure required fields exist
                chunk.setdefault("content", "")
                chunk.setdefault("document_id", "")
                chunk.setdefault("doc_type", "text")
        
        return formatted
    
    async def chat_stream(
        self,
        message: str,
        api_key: str,
        base_url: str,
        chat_id: str,
        session_id: str
    ) -> Iterator[Dict[str, Any]]:
        """
        Stream chat response with enhanced processing
        """
        try:
            ragflow = self.get_ragflow_client(api_key, base_url)
            chat = self.get_chat_instance(ragflow, chat_id)
            session = self.get_session_instance(chat, session_id)
            
            logger.info(f"Starting streaming chat for session {session.id}")
            
            # Use the session's ask method with streaming
            response_generator = session.ask(question=message, stream=True)
            
            accumulated_content = ""
            final_reference = None
            
            for message_obj in response_generator:
                if isinstance(message_obj, Message):
                    content = message_obj.content
                    accumulated_content = content
                    
                    # Process content for thinking and citations
                    processed = self.process_content_for_citations(content)
                    
                    # Get reference data if available
                    reference = getattr(message_obj, 'reference', None)
                    if reference:
                        final_reference = self.format_references_for_frontend(reference)
                    
                    yield {
                        "content": processed["processed_content"],
                        "original_content": processed["original_content"],
                        "thinking_process": processed["thinking_process"],
                        "citation_markers": processed["citation_markers"],
                        "references": final_reference,
                        "session_id": session.id,
                        "is_complete": False
                    }
            
            # Send final completion event
            yield {
                "content": accumulated_content,
                "is_complete": True,
                "session_id": session.id,
                "references": final_reference
            }
            
        except Exception as e:
            logger.error(f"Error in chat_stream: {e}")
            yield {
                "error": str(e),
                "is_complete": True
            }
    
    async def chat_complete(
        self,
        message: str,
        api_key: str,
        base_url: str,
        chat_id: str,
        session_id: str
    ) -> Dict[str, Any]:
        """
        Get complete chat response (non-streaming)
        """
        try:
            ragflow = self.get_ragflow_client(api_key, base_url)
            chat = self.get_chat_instance(ragflow, chat_id)
            session = self.get_session_instance(chat, session_id)
            
            logger.info(f"Starting non-streaming chat for session {session.id}")
            
            # Use the session's ask method without streaming
            message_obj = session.ask(question=message, stream=False)
            
            if isinstance(message_obj, Message):
                content = message_obj.content
                
                # Process content for thinking and citations
                processed = self.process_content_for_citations(content)
                
                # Get reference data if available
                reference = getattr(message_obj, 'reference', None)
                formatted_reference = self.format_references_for_frontend(reference) if reference else {}
                
                return {
                    "content": processed["processed_content"],
                    "original_content": processed["original_content"],
                    "thinking_process": processed["thinking_process"],
                    "citation_markers": processed["citation_markers"],
                    "references": formatted_reference,
                    "session_id": session.id
                }
            else:
                raise Exception("Invalid response type from RAGFlow")
                
        except Exception as e:
            logger.error(f"Error in chat_complete: {e}")
            raise Exception(f"RAGFlow chat error: {str(e)}")

# Global service instance
ragflow_service = RAGFlowEnhancedService()
