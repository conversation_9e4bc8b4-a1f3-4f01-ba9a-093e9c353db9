import os
import json
import asyncio
import logging
from typing import Dict, Optional, List, Any
from contextlib import asynccontextmanager

import httpx
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse
from dotenv import load_dotenv

from ragflow_service import ragflow_service

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global HTTP client
http_client: Optional[httpx.AsyncClient] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global http_client
    http_client = httpx.AsyncClient(timeout=30.0)
    logger.info("Python RAGFlow service started")
    yield
    # Shutdown
    if http_client:
        await http_client.aclose()
    logger.info("Python RAGFlow service stopped")

app = FastAPI(
    title="RAGFlow Enhanced Chat Service",
    description="Python FastAPI service for enhanced RAGFlow integration with citations, thinking process, and document references",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class RAGFlowConfig(BaseModel):
    api_key: str
    base_url: str
    chat_id: str

class ChatRequest(BaseModel):
    message: str
    session_id: str
    ragflow_config: RAGFlowConfig
    stream: bool = True

class ChatResponse(BaseModel):
    content: str
    session_id: Optional[str] = None
    references: Optional[Dict[str, Any]] = None
    thinking_process: Optional[str] = None

# In-memory session storage (replace with Redis in production)
session_storage: Dict[str, str] = {}

@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """
    Stream chat response from RAGFlow with enhanced processing using Python SDK
    """
    try:
        async def generate_events():
            async for event_data in ragflow_service.chat_stream(
                message=request.message,
                api_key=request.ragflow_config.api_key,
                base_url=request.ragflow_config.base_url,
                chat_id=request.ragflow_config.chat_id,
                session_id=request.session_id
            ):
                # Store session mapping if we get a new session ID
                if event_data.get('session_id') and event_data['session_id'] != request.session_id:
                    session_storage[request.session_id] = event_data['session_id']

                yield f"data: {json.dumps(event_data)}\n\n"

        return EventSourceResponse(generate_events())

    except Exception as e:
        logger.error(f"Error in chat_stream: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat/complete")
async def chat_complete(request: ChatRequest) -> ChatResponse:
    """
    Get complete chat response from RAGFlow (non-streaming) using Python SDK
    """
    try:
        result = await ragflow_service.chat_complete(
            message=request.message,
            api_key=request.ragflow_config.api_key,
            base_url=request.ragflow_config.base_url,
            chat_id=request.ragflow_config.chat_id,
            session_id=request.session_id
        )

        # Store session mapping if we get a new session ID
        if result.get('session_id') and result['session_id'] != request.session_id:
            session_storage[request.session_id] = result['session_id']

        return ChatResponse(
            content=result["content"],
            session_id=result.get("session_id"),
            references=result.get("references", {}),
            thinking_process=result.get("thinking_process")
        )

    except Exception as e:
        logger.error(f"Error in chat_complete: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "ragflow-enhanced-chat"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
