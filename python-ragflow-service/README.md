# Enhanced RAGFlow Service

This Python FastAPI service provides enhanced RAGFlow integration for the JURBOT application, featuring:

- **Citations**: Interactive citation markers with popover details
- **Thinking Process**: Display of RAG<PERSON>low's reasoning process
- **Document References**: List of source documents with links
- **Streaming Support**: Real-time response streaming
- **Fallback Support**: Graceful degradation to basic RAGFlow API

## Features

### 1. Citation System
- Parses `[ID:n]` markers from RAGFlow responses
- Displays interactive info icons with citation details
- Shows document snippets, similarity scores, and source links

### 2. Thinking Process
- Extracts `<think></think>` tags from responses
- Displays reasoning process in a styled section
- Supports both streaming and complete responses

### 3. Document References
- Lists all referenced documents at the end of responses
- Provides clickable links to source documents
- Shows document icons and names

### 4. Streaming Support
- Server-Sent Events (SSE) for real-time updates
- Incremental content delivery
- Live citation and thinking process updates

## Architecture

```
Node.js Backend (Express)
    ↓
Python FastAPI Service
    ↓
RAGFlow Python SDK
    ↓
RAGFlow API
```

## Installation

1. **Install Python dependencies:**
   ```bash
   cd python-ragflow-service
   pip install -r requirements.txt
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Run the service:**
   ```bash
   python main.py
   ```

## Docker Setup

The service is integrated with the main JURBOT docker-compose.yml:

```yaml
python-ragflow-service:
  build: ./python-ragflow-service
  ports:
    - "8001:8001"
  volumes:
    - ./ragflow:/app/ragflow:ro
  environment:
    - LOG_LEVEL=INFO
    - CORS_ORIGINS=*
```

## Quick Deployment

### Automated Deployment Scripts

**Development Environment:**
```bash
# Automated deployment with health checks
./deploy.sh

# Show logs after deployment
./deploy.sh --logs

# Skip waiting for services
./deploy.sh --no-wait
```

**Production Environment:**
```bash
# Production deployment with Redis
./deploy-production.sh

# Production with monitoring
./deploy-production.sh --monitoring
```

**Service Management:**
```bash
# Start services
./manage.sh start dev    # Development
./manage.sh start prod   # Production

# Check health
./manage.sh health

# View logs
./manage.sh logs python-ragflow-service

# Run tests
./manage.sh test

# Monitor continuously
./manage.sh monitor
```

## API Endpoints

### POST /chat/stream
Streaming chat with enhanced features.

**Request:**
```json
{
  "message": "Your question here",
  "session_id": "unique-session-id",
  "ragflow_config": {
    "api_key": "your-api-key",
    "base_url": "http://ragflow-server",
    "chat_id": "your-chat-id"
  },
  "stream": true
}
```

**Response (SSE):**
```
data: {
  "content": "Processed response content",
  "original_content": "Raw response with markers",
  "thinking_process": "RAGFlow's reasoning",
  "citation_markers": [0, 1, 2],
  "references": {
    "chunks": [...],
    "doc_aggs": [...]
  },
  "is_complete": false
}
```

### POST /chat/complete
Non-streaming chat completion.

**Request:** Same as streaming
**Response:** Single JSON object with complete data

### GET /health
Health check endpoint.

## Integration with JURBOT

### Node.js Backend Integration

The Node.js backend proxies requests to the Python service:

```javascript
// Enhanced RAGFlow endpoint
app.post("/api/ragflow/enhanced-stream", async (req, res) => {
  const pythonServiceResponse = await fetch(
    `${PYTHON_RAGFLOW_SERVICE_URL}/chat/stream`,
    {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(req.body)
    }
  );
  
  // Stream response to client
  pythonServiceResponse.body.pipeTo(res);
});
```

### Frontend Integration

The React frontend uses enhanced components:

```typescript
// Enhanced streaming hook
const { startEnhancedStreaming } = useEnhancedRAGFlowStreaming({
  onComplete: (data) => {
    // Handle citations, thinking, references
  }
});

// Enhanced markdown component
<EnhancedRAGFlowMarkdown
  content={content}
  thinkingProcess={thinking}
  citationMarkers={citations}
  references={references}
/>
```

## Configuration

### Environment Variables

- `PYTHON_RAGFLOW_SERVICE_PORT`: Service port (default: 8001)
- `LOG_LEVEL`: Logging level (default: INFO)
- `CORS_ORIGINS`: CORS allowed origins (default: *)

### RAGFlow Configuration

Configure in the JURBOT frontend:
- API Key: Your RAGFlow API key
- Base URL: RAGFlow server URL
- Chat ID: RAGFlow chat/assistant ID

## Development

### Running Locally

1. Start the Python service:
   ```bash
   cd python-ragflow-service
   uvicorn main:app --reload --port 8001
   ```

2. Update Node.js environment:
   ```bash
   export PYTHON_RAGFLOW_SERVICE_URL=http://localhost:8001
   ```

3. Start the main application:
   ```bash
   npm run dev
   ```

### Testing

Test the enhanced streaming:
```bash
curl -X POST http://localhost:8001/chat/stream \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Test question",
    "session_id": "test-session",
    "ragflow_config": {
      "api_key": "your-key",
      "base_url": "http://ragflow-server",
      "chat_id": "your-chat-id"
    }
  }'
```

## Troubleshooting

### Common Issues

1. **Python service not accessible:**
   - Check if service is running on port 8001
   - Verify PYTHON_RAGFLOW_SERVICE_URL in Node.js

2. **RAGFlow SDK import errors:**
   - Ensure RAGFlow repository is mounted at `/app/ragflow`
   - Check Python path configuration

3. **Citation markers not working:**
   - Verify RAGFlow is returning `[ID:n]` markers
   - Check reference data structure

4. **Streaming not working:**
   - Check SSE headers and CORS configuration
   - Verify client-side EventSource handling

### Logs

Check service logs:
```bash
docker-compose logs python-ragflow-service
```

## Future Enhancements

- Redis session storage for production
- WebSocket support for better streaming
- Citation caching and optimization
- Advanced reference visualization
- Multi-language support for thinking process
