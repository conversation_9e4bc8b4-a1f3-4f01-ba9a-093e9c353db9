import { apiRequest } from "./queryClient";
import { getChatbotConfig, getApiHeaders, validateConfig } from "./chatbot-config";
import type { InsertFolder, InsertChatSession, InsertMessage, UpdateChatSession, Folder, ChatSession, Message } from "@shared/schema";

// API base path for production
const API_BASE = import.meta.env.PROD ? "/jurbot-chat/api" : "/api";

export const chatApi = {
  // Folders
  getFolders: async (): Promise<Folder[]> => {
    const response = await apiRequest("GET", `${API_BASE}/folders`);
    return response.json();
  },

  createFolder: async (folder: InsertFolder): Promise<Folder> => {
    const response = await apiRequest("POST", `${API_BASE}/folders`, folder);
    return response.json();
  },

  updateFolder: async (id: number, folder: Partial<InsertFolder>): Promise<Folder> => {
    const response = await apiRequest("PUT", `${API_BASE}/folders/${id}`, folder);
    return response.json();
  },

  deleteFolder: async (id: number): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `${API_BASE}/folders/${id}`);
    return response.json();
  },

  // Chat Sessions
  getChatSessions: async (): Promise<ChatSession[]> => {
    const response = await apiRequest("GET", `${API_BASE}/chat-sessions`);
    return response.json();
  },

  createChatSession: async (session: InsertChatSession): Promise<ChatSession> => {
    const response = await apiRequest("POST", `${API_BASE}/chat-sessions`, session);
    return response.json();
  },

  updateChatSession: async (id: string, session: UpdateChatSession): Promise<ChatSession> => {
    const response = await apiRequest("PUT", `${API_BASE}/chat-sessions/${id}`, session);
    return response.json();
  },

  deleteChatSession: async (id: string): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `${API_BASE}/chat-sessions/${id}`);
    return response.json();
  },

  moveChatSession: async (sessionId: string, folderId: number | null): Promise<{ success: boolean }> => {
    const response = await apiRequest("POST", `${API_BASE}/chat-sessions/${sessionId}/move`, { folderId });
    return response.json();
  },

  // Messages
  getMessages: async (sessionId: string): Promise<Message[]> => {
    const response = await apiRequest("GET", `${API_BASE}/chat-sessions/${sessionId}/messages`);
    return response.json();
  },

  sendMessage: async (sessionId: string, content: string): Promise<{ userMessage: Message; aiMessage: Message }> => {
    // Get and validate chatbot configuration
    const config = getChatbotConfig();
    const validation = validateConfig(config);

    if (!validation.isValid) {
      throw new Error(`Configuration error: ${validation.errors.join(", ")}`);
    }

    // Get appropriate headers for the selected engine
    const headers = getApiHeaders(config);

    try {
      const response = await fetch(`${API_BASE}/chat-sessions/${sessionId}/messages`, {
        method: "POST",
        headers,
        body: JSON.stringify({
          content,
          role: "user",
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      return response.json();
    } catch (error) {
      console.error("Error sending message:", error);

      // Provide more specific error messages based on the engine
      if (config.engine === "ragflow") {
        throw new Error("Failed to send message to RAGFlow. Please check your RAGFlow configuration and try again.");
      } else {
        throw new Error("Failed to send message to n8n. Please check your webhook configuration and try again.");
      }
    }
  },

  clearMessages: async (sessionId: string): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `${API_BASE}/chat-sessions/${sessionId}/messages`);
    return response.json();
  },

  // RAGFlow streaming (legacy endpoint)
  streamRAGFlowMessage: async (
    sessionId: string,
    message: string,
    onChunk: (data: {
      content?: string;
      reference?: any;
      thinking?: string;
      session_id?: string;
      id?: string;
    }) => void,
    onError: (error: string) => void,
    onComplete: () => void
  ): Promise<void> => {
    const config = getChatbotConfig();
    const validation = validateConfig(config);

    if (!validation.isValid) {
      throw new Error(`Configuration error: ${validation.errors.join(", ")}`);
    }

    if (config.engine !== 'ragflow') {
      throw new Error('Streaming is only available for RAGFlow engine');
    }

    try {
      const response = await fetch(`${API_BASE}/ragflow-stream`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message,
          sessionId,
          ragflowConfig: config.ragflow,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body available for streaming');
      }

      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            onComplete();
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.trim() === '') continue;

            if (line.startsWith('data: ')) {
              const dataStr = line.substring(6);

              if (dataStr === '[DONE]') {
                onComplete();
                return;
              }

              try {
                const data = JSON.parse(dataStr);
                onChunk(data);
              } catch (parseError) {
                console.error('Error parsing streaming data:', parseError);
                onError('Error parsing streaming response');
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error("Error in RAGFlow streaming:", error);
      onError(error instanceof Error ? error.message : 'Unknown streaming error');
    }
  },

  // Enhanced RAGFlow streaming with Python service
  streamRAGFlowEnhanced: async (
    sessionId: string,
    message: string,
    onChunk: (data: {
      content?: string;
      original_content?: string;
      thinking_process?: string;
      citation_markers?: number[];
      references?: any;
      session_id?: string;
      is_complete?: boolean;
      error?: string;
      fallback_available?: boolean;
    }) => void,
    onError: (error: string) => void,
    onComplete: () => void
  ): Promise<void> => {
    const config = getChatbotConfig();
    const validation = validateConfig(config);

    if (!validation.isValid) {
      throw new Error(`Configuration error: ${validation.errors.join(", ")}`);
    }

    if (config.engine !== 'ragflow') {
      throw new Error('Enhanced streaming is only available for RAGFlow engine');
    }

    try {
      const response = await fetch(`${API_BASE}/ragflow/enhanced-stream`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message,
          sessionId,
          ragflowConfig: config.ragflow,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body available for streaming');
      }

      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            onComplete();
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.trim() === '') continue;

            if (line.startsWith('data: ')) {
              const dataStr = line.substring(6);

              if (dataStr === '[DONE]') {
                onComplete();
                return;
              }

              try {
                const data = JSON.parse(dataStr);

                // Handle completion
                if (data.is_complete) {
                  onComplete();
                  return;
                }

                onChunk(data);
              } catch (parseError) {
                console.error('Error parsing enhanced streaming data:', parseError);
                onError('Error parsing enhanced streaming response');
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error("Error in enhanced RAGFlow streaming:", error);
      onError(error instanceof Error ? error.message : 'Unknown enhanced streaming error');
    }
  },
};
