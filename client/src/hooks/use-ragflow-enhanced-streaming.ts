import { useState, useCallback } from "react";
import { chatApi } from "@/lib/chat-api";
import { getChatbotConfig } from "@/lib/chatbot-config";

interface EnhancedStreamingData {
  content?: string;
  original_content?: string;
  thinking_process?: string;
  citation_markers?: number[];
  references?: {
    chunks?: Array<{
      index?: number;
      content: string;
      document_id: string;
      doc_type?: string;
      image_id?: string;
      similarity?: number;
    }>;
    doc_aggs?: Array<{
      doc_id: string;
      doc_name: string;
      url?: string;
    }>;
    total?: number;
  };
  session_id?: string;
  is_complete?: boolean;
  error?: string;
  fallback_available?: boolean;
}

interface UseEnhancedRAGFlowStreamingProps {
  onThinkingUpdate?: (thinking: string) => void;
  onContentUpdate?: (content: string, originalContent?: string) => void;
  onReferenceUpdate?: (references: any) => void;
  onCitationUpdate?: (citationMarkers: number[]) => void;
  onComplete?: (finalData: {
    content: string;
    originalContent?: string;
    references?: any;
    thinking?: string;
    citationMarkers?: number[];
  }) => void;
  onError?: (error: string, fallbackAvailable?: boolean) => void;
}

export function useEnhancedRAGFlowStreaming({
  onThinkingUpdate,
  onContentUpdate,
  onReferenceUpdate,
  onCitationUpdate,
  onComplete,
  onError,
}: UseEnhancedRAGFlowStreamingProps = {}) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingContent, setStreamingContent] = useState("");
  const [streamingOriginalContent, setStreamingOriginalContent] = useState("");
  const [streamingThinking, setStreamingThinking] = useState("");
  const [streamingReferences, setStreamingReferences] = useState<any>(null);
  const [streamingCitations, setStreamingCitations] = useState<number[]>([]);

  const startEnhancedStreaming = useCallback(async (sessionId: string, message: string) => {
    const config = getChatbotConfig();
    
    if (config.engine !== 'ragflow') {
      onError?.('Enhanced streaming is only available for RAGFlow engine');
      return;
    }

    setIsStreaming(true);
    setStreamingContent("");
    setStreamingOriginalContent("");
    setStreamingThinking("");
    setStreamingReferences(null);
    setStreamingCitations([]);

    let accumulatedContent = "";
    let accumulatedOriginalContent = "";
    let latestThinking = "";
    let latestReferences: any = null;
    let latestCitations: number[] = [];

    try {
      await chatApi.streamRAGFlowEnhanced(
        sessionId,
        message,
        (data: EnhancedStreamingData) => {
          // Handle content updates
          if (data.content !== undefined) {
            accumulatedContent = data.content;
            setStreamingContent(accumulatedContent);
            onContentUpdate?.(accumulatedContent, data.original_content);
          }

          // Handle original content
          if (data.original_content !== undefined) {
            accumulatedOriginalContent = data.original_content;
            setStreamingOriginalContent(accumulatedOriginalContent);
          }

          // Handle thinking process updates
          if (data.thinking_process !== undefined && data.thinking_process !== latestThinking) {
            latestThinking = data.thinking_process;
            setStreamingThinking(latestThinking);
            onThinkingUpdate?.(latestThinking);
          }

          // Handle reference updates
          if (data.references && Object.keys(data.references).length > 0) {
            latestReferences = data.references;
            setStreamingReferences(latestReferences);
            onReferenceUpdate?.(latestReferences);
          }

          // Handle citation markers
          if (data.citation_markers && data.citation_markers.length > 0) {
            latestCitations = data.citation_markers;
            setStreamingCitations(latestCitations);
            onCitationUpdate?.(latestCitations);
          }

          // Handle errors with fallback option
          if (data.error) {
            console.error('Enhanced RAGFlow streaming error:', data.error);
            setIsStreaming(false);
            onError?.(data.error, data.fallback_available);
          }
        },
        (error: string) => {
          console.error('Enhanced RAGFlow streaming error:', error);
          setIsStreaming(false);
          onError?.(error, true); // Assume fallback is available on network errors
        },
        () => {
          setIsStreaming(false);
          onComplete?.({
            content: accumulatedContent,
            originalContent: accumulatedOriginalContent,
            references: latestReferences,
            thinking: latestThinking,
            citationMarkers: latestCitations,
          });
        }
      );
    } catch (error) {
      console.error('Error starting enhanced RAGFlow stream:', error);
      setIsStreaming(false);
      onError?.(error instanceof Error ? error.message : 'Unknown enhanced streaming error', true);
    }
  }, [onThinkingUpdate, onContentUpdate, onReferenceUpdate, onCitationUpdate, onComplete, onError]);

  const stopStreaming = useCallback(() => {
    setIsStreaming(false);
    // Note: We can't actually stop the server-side stream, but we can stop processing it
  }, []);

  const clearStreamingData = useCallback(() => {
    setStreamingContent("");
    setStreamingOriginalContent("");
    setStreamingThinking("");
    setStreamingReferences(null);
    setStreamingCitations([]);
  }, []);

  return {
    isStreaming,
    streamingContent,
    streamingOriginalContent,
    streamingThinking,
    streamingReferences,
    streamingCitations,
    startEnhancedStreaming,
    stopStreaming,
    clearStreamingData,
  };
}
