import React, { useState, useMemo, useCallback } from "react";
import { Info, FileText, Image as ImageIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

interface EnhancedRAGFlowMarkdownProps {
  content: string;
  originalContent?: string;
  thinkingProcess?: string;
  citationMarkers?: number[];
  references?: {
    chunks?: Array<{
      index?: number;
      content: string;
      document_id: string;
      doc_type?: string;
      image_id?: string;
      similarity?: number;
    }>;
    doc_aggs?: Array<{
      doc_id: string;
      doc_name: string;
      url?: string;
    }>;
    total?: number;
  };
  className?: string;
}

// Helper function to convert <think></think> tags to styled sections
function replaceThinkToSection(text: string): string {
  return text.replace(
    /<think>([\s\S]*?)<\/think>/g,
    '<section class="think">$1</section>'
  );
}

export function EnhancedRAGFlowMarkdown({
  content,
  originalContent,
  thinkingProcess,
  citationMarkers = [],
  references,
  className = ""
}: EnhancedRAGFlowMarkdownProps) {
  const [expandedCitations, setExpandedCitations] = useState<Set<number>>(new Set());

  // Process content to handle thinking sections and prepare for citation replacement
  const processedContent = useMemo(() => {
    if (!content) return '';
    
    // Convert <think></think> tags to styled sections
    return replaceThinkToSection(content);
  }, [content]);

  // Get reference information for a citation index
  const getReferenceInfo = useCallback((citationIndex: number) => {
    if (!references?.chunks || !references?.doc_aggs) {
      return null;
    }

    const chunk = references.chunks[citationIndex];
    if (!chunk) return null;

    const document = references.doc_aggs.find(doc => doc.doc_id === chunk.document_id);
    
    return {
      chunk,
      document
    };
  }, [references]);

  // Create citation popover content
  const createCitationPopover = useCallback((citationIndex: number) => {
    const refInfo = getReferenceInfo(citationIndex);
    if (!refInfo) return null;

    const { chunk, document } = refInfo;

    return (
      <Card className="max-w-md border-0 shadow-lg">
        <CardContent className="p-3 space-y-2">
          {/* Show image if available */}
          {chunk.image_id && (
            <div className="flex justify-center">
              <div className="w-20 h-20 bg-gray-100 rounded flex items-center justify-center">
                <ImageIcon className="h-8 w-8 text-gray-400" />
                <span className="text-xs text-gray-500 ml-1">Image</span>
              </div>
            </div>
          )}
          
          {/* Chunk content */}
          <div className="text-sm text-muted-foreground max-h-32 overflow-y-auto">
            {chunk.content}
          </div>
          
          {/* Document info */}
          {document && (
            <div className="flex items-center gap-2 pt-2 border-t">
              <FileText className="h-4 w-4 text-blue-500" />
              <Button
                variant="link"
                size="sm"
                className="h-auto p-0 text-xs text-blue-600 hover:text-blue-800"
                onClick={() => {
                  if (document.url) {
                    window.open(document.url, '_blank');
                  }
                }}
              >
                {document.doc_name}
              </Button>
            </div>
          )}
          
          {/* Similarity score */}
          {chunk.similarity !== undefined && (
            <div className="text-xs text-muted-foreground">
              Similarity: {Math.round(chunk.similarity * 100)}%
            </div>
          )}
        </CardContent>
      </Card>
    );
  }, [getReferenceInfo]);

  // Replace citation markers with interactive elements
  const renderWithCitations = useCallback((text: string) => {
    if (!citationMarkers.length || !references?.chunks) {
      return text;
    }

    // Replace [ID:n] markers with citation components
    const citationRegex = /\[ID:(\d+)\]/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = citationRegex.exec(text)) !== null) {
      const citationIndex = parseInt(match[1]);
      
      // Add text before the citation
      if (match.index > lastIndex) {
        parts.push(text.slice(lastIndex, match.index));
      }

      // Add citation component
      const refInfo = getReferenceInfo(citationIndex);
      if (refInfo) {
        const popoverContent = createCitationPopover(citationIndex);
        
        parts.push(
          <Popover key={`citation-${citationIndex}-${match.index}`}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 mx-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50"
              >
                <Info className="h-3 w-3" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" side="top">
              {popoverContent}
            </PopoverContent>
          </Popover>
        );
      } else {
        // Fallback for missing reference
        parts.push(
          <span key={`citation-missing-${citationIndex}-${match.index}`} className="text-gray-400">
            [{citationIndex}]
          </span>
        );
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }

    return parts;
  }, [citationMarkers, references, getReferenceInfo, createCitationPopover]);

  return (
    <div className={`ragflow-enhanced-markdown ${className}`}>
      {/* Thinking process section */}
      {thinkingProcess && (
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border-l-4 border-gray-300 dark:border-gray-600">
          <div className="text-xs text-gray-500 dark:text-gray-400 mb-1 font-medium">
            Thinking Process
          </div>
          <div className="text-sm text-gray-700 dark:text-gray-300 italic">
            {thinkingProcess}
          </div>
        </div>
      )}

      {/* Main content */}
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        className="prose prose-sm max-w-none dark:prose-invert"
        components={{
          p: ({ children }) => (
            <p className="mb-2 last:mb-0">
              {renderWithCitations(String(children))}
            </p>
          ),
          div: ({ children, className: divClassName }) => {
            // Handle think sections
            if (divClassName === 'think') {
              return (
                <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border-l-4 border-gray-300 dark:border-gray-600">
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-1 font-medium">
                    Thinking
                  </div>
                  <div className="text-sm text-gray-700 dark:text-gray-300 italic">
                    {children}
                  </div>
                </div>
              );
            }
            return <div className={divClassName}>{children}</div>;
          },
          section: ({ children, className: sectionClassName }) => {
            // Handle think sections
            if (sectionClassName === 'think') {
              return (
                <section className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border-l-4 border-gray-300 dark:border-gray-600">
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-1 font-medium">
                    Thinking
                  </div>
                  <div className="text-sm text-gray-700 dark:text-gray-300 italic">
                    {children}
                  </div>
                </section>
              );
            }
            return <section className={sectionClassName}>{children}</section>;
          },
        }}
      >
        {processedContent}
      </ReactMarkdown>

      {/* Document references list */}
      {references?.doc_aggs && references.doc_aggs.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            References ({references.doc_aggs.length})
          </div>
          <div className="space-y-2">
            {references.doc_aggs.map((doc, index) => (
              <div key={doc.doc_id} className="flex items-center gap-2 text-sm">
                <FileText className="h-4 w-4 text-blue-500 flex-shrink-0" />
                <Button
                  variant="link"
                  size="sm"
                  className="h-auto p-0 text-blue-600 hover:text-blue-800 text-left"
                  onClick={() => {
                    if (doc.url) {
                      window.open(doc.url, '_blank');
                    }
                  }}
                >
                  {doc.doc_name}
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Custom CSS for think sections */}
      <style jsx>{`
        .ragflow-enhanced-markdown :global(.think) {
          padding-left: 10px;
          color: #8b8b8b;
          border-left: 2px solid #d5d3d3;
          margin-bottom: 10px;
          font-size: 12px;
        }
      `}</style>
    </div>
  );
}
