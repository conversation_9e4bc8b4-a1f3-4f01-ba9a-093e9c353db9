import { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { Info, FileText } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import reactStringReplace from "react-string-replace";

// RAGFlow citation regex - matches [ID:n] markers
const citationRegex = /\[ID:(\d+)\]/g;

// Function to convert <think></think> tags to styled sections
const replaceThinkToSection = (text: string = '') => {
  return text.replace(/<think>([\s\S]*?)<\/think>/g, '<section class="think">$1</section>');
};

interface ReferenceChunk {
  id: string;
  content: string;
  document_id: string;
  document_name: string;
  similarity: number;
  vector_similarity: number;
  term_similarity: number;
}

interface ReferenceDocAgg {
  doc_id: string;
  doc_name: string;
  url?: string;
}

interface ReferenceData {
  chunks: ReferenceChunk[];
  doc_aggs: ReferenceDocAgg[];
  total: number;
}

interface RAGFlowMarkdownProps {
  content: string;
  reference?: ReferenceData;
  className?: string;
}

export function RAGFlowMarkdown({ content, reference, className = "" }: RAGFlowMarkdownProps) {
  const [expandedCitations, setExpandedCitations] = useState<Set<number>>(new Set());

  // Process content to handle thinking sections and prepare for citation replacement
  const processedContent = useMemo(() => {
    if (!content) return '';
    
    // Convert <think></think> tags to styled sections
    return replaceThinkToSection(content);
  }, [content]);

  // Get reference info for a specific chunk index
  const getReferenceInfo = useCallback((chunkIndex: number) => {
    if (!reference?.chunks || chunkIndex >= reference.chunks.length) {
      return null;
    }

    const chunk = reference.chunks[chunkIndex];
    const document = reference.doc_aggs?.find(doc => doc.doc_id === chunk.document_id);

    return {
      chunk,
      document,
    };
  }, [reference]);

  // Create popover content for citation
  const getCitationPopover = useCallback((chunkIndex: number) => {
    const refInfo = getReferenceInfo(chunkIndex);
    if (!refInfo) return null;

    const { chunk, document } = refInfo;

    return (
      <Card className="max-w-md border-0 shadow-lg">
        <CardContent className="p-3 space-y-2">
          <div className="text-sm text-muted-foreground max-h-32 overflow-y-auto">
            {chunk.content}
          </div>
          {document && (
            <div className="flex items-center gap-2 pt-2 border-t">
              <FileText className="h-4 w-4 text-blue-500" />
              <Button
                variant="link"
                size="sm"
                className="h-auto p-0 text-xs text-blue-600 hover:text-blue-800"
                onClick={() => {
                  if (document.url) {
                    window.open(document.url, '_blank');
                  }
                }}
              >
                {document.doc_name}
              </Button>
            </div>
          )}
          <div className="text-xs text-muted-foreground">
            Similarity: {Math.round(chunk.similarity * 100)}%
          </div>
        </CardContent>
      </Card>
    );
  }, [getReferenceInfo]);

  // Replace citation markers with interactive elements
  const renderWithCitations = useCallback((text: string) => {
    return reactStringReplace(text, citationRegex, (match, i) => {
      const chunkIndex = parseInt(match, 10);
      const popoverContent = getCitationPopover(chunkIndex);

      if (!popoverContent) {
        return `[ID:${match}]`; // Return original if no reference found
      }

      return (
        <Popover key={i}>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0 mx-1 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
            >
              <Info className="h-3 w-3" />
            </Button>
          </PopoverTrigger>
          <PopoverContent side="top" className="p-0">
            {popoverContent}
          </PopoverContent>
        </Popover>
      );
    });
  }, [getCitationPopover]);

  return (
    <div className={`ragflow-markdown ${className}`}>
      <style jsx>{`
        .ragflow-markdown :global(section.think) {
          padding-left: 10px;
          color: #8b8b8b;
          border-left: 2px solid #d5d3d3;
          margin-bottom: 10px;
          font-size: 12px;
          background-color: rgba(139, 139, 139, 0.05);
          border-radius: 4px;
          padding: 8px 12px;
        }
        
        .ragflow-markdown :global(section.think):before {
          content: "💭 Thinking...";
          display: block;
          font-weight: 500;
          margin-bottom: 4px;
          color: #666;
        }
      `}</style>
      
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        className="prose prose-sm max-w-none dark:prose-invert"
        components={{
          p: ({ children }) => <p className="mb-2 last:mb-0">{renderWithCitations(String(children))}</p>,
          div: ({ children, className }) => {
            // Handle think sections
            if (className === 'think') {
              return <section className="think">{children}</section>;
            }
            return <div className={className}>{children}</div>;
          },
          section: ({ children, className }) => {
            // Handle think sections
            if (className === 'think') {
              return <section className="think">{children}</section>;
            }
            return <section className={className}>{children}</section>;
          },
          // Handle other markdown elements
          ul: ({ children }) => <ul className="mb-2 last:mb-0 ml-4">{children}</ul>,
          ol: ({ children }) => <ol className="mb-2 last:mb-0 ml-4">{children}</ol>,
          li: ({ children }) => <li className="mb-1">{children}</li>,
          code: ({ children }) => <code className="bg-black/20 dark:bg-white/20 px-1 py-0.5 rounded text-sm">{children}</code>,
          pre: ({ children }) => <pre className="bg-black/20 dark:bg-white/20 p-2 rounded text-sm overflow-x-auto">{children}</pre>,
          blockquote: ({ children }) => <blockquote className="border-l-2 border-black/30 dark:border-white/30 pl-4 italic">{children}</blockquote>,
          a: ({ href, children }) => <a href={href} className="text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 underline break-all" target="_blank" rel="noopener noreferrer">{children}</a>,
          h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
          h2: ({ children }) => <h2 className="text-base font-bold mb-2">{children}</h2>,
          h3: ({ children }) => <h3 className="text-sm font-bold mb-1">{children}</h3>,
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
}
