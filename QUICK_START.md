# 🚀 JURBOT Enhanced RAGFlow - Quick Start Guide

Get your JURBOT application with Enhanced RAGFlow features up and running in minutes!

## 📋 Prerequisites

- **Docker** and **Docker Compose** installed
- **RAGFlow instance** running and accessible
- **RAGFlow API credentials** (API key, base URL, chat ID)

## ⚡ Quick Deployment

### 1. **Automated Development Setup**

```bash
# Clone and navigate to your JURBOT directory
cd /home/<USER>/jurbot

# Run automated deployment
./deploy.sh

# The script will:
# ✅ Check prerequisites
# ✅ Setup environment files
# ✅ Build and start all services
# ✅ Run health checks
# ✅ Show service URLs
```

### 2. **Configure RAGFlow Settings**

Edit the Python service configuration:
```bash
nano python-ragflow-service/.env
```

Add your RAGFlow credentials:
```env
RAGFLOW_API_KEY=your_actual_api_key_here
RAGFLOW_BASE_URL=http://**************
RAGFLOW_CHAT_ID=your_actual_chat_id_here
```

### 3. **Restart Services**

```bash
./manage.sh restart dev
```

### 4. **Verify Everything Works**

```bash
# Check service health
./manage.sh health

# Run comprehensive tests (optional)
python python-ragflow-service/test_client.py --test all \
  --ragflow-api-key "your_key" \
  --ragflow-base-url "http://**************" \
  --ragflow-chat-id "your_chat_id"
```

## 🌐 Access Your Application

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5001
- **Enhanced RAGFlow Service**: http://localhost:8001
- **Database**: localhost:5440

## 🎯 Configure Frontend

1. Open JURBOT frontend at http://localhost:5173
2. Go to **Settings** → **Chatbot Configuration**
3. Select **RAGFlow** as the engine
4. Enter your RAGFlow credentials:
   - **API Key**: Your RAGFlow API key
   - **Base URL**: Your RAGFlow server URL
   - **Chat ID**: Your RAGFlow assistant/chat ID

## ✨ Enhanced Features

Your JURBOT now includes:

### 📚 **Interactive Citations**
- Click on blue info icons (ℹ️) in responses
- View source document snippets
- Access original documents

### 🧠 **Thinking Process**
- See RAGFlow's reasoning process
- Displayed in grey boxes above responses
- Shows how the AI arrived at its answer

### 📄 **Document References**
- List of all referenced documents
- Clickable links to source materials
- Appears at the bottom of AI responses

### 🌊 **Real-time Streaming**
- Live response generation
- Incremental content updates
- Enhanced streaming with citations

## 🔧 Common Commands

```bash
# Service management
./manage.sh start dev        # Start development environment
./manage.sh stop dev         # Stop services
./manage.sh restart dev      # Restart services
./manage.sh logs            # View all logs
./manage.sh health          # Check service health
./manage.sh status          # Show service status

# Debugging and testing
./manage.sh debug           # Run debug checks
./manage.sh test            # Run service tests
./manage.sh monitor         # Continuous monitoring

# Maintenance
./manage.sh backup          # Backup data
./manage.sh update dev      # Update and rebuild
./manage.sh clean           # Clean up (removes all data!)
```

## 🏭 Production Deployment

For production with Redis session storage and optimizations:

```bash
# Production deployment
./deploy-production.sh

# Start production services
./manage.sh start prod

# Production with monitoring
./deploy-production.sh --monitoring
```

## 🔍 Troubleshooting

### Service Not Starting?
```bash
# Check what's wrong
./manage.sh debug

# View logs
./manage.sh logs python-ragflow-service

# Check Docker status
docker-compose ps
```

### Citations Not Working?
1. Verify RAGFlow configuration in frontend settings
2. Check if RAGFlow is returning citation markers:
   ```bash
   curl -X POST http://localhost:8001/chat/complete \
     -H "Content-Type: application/json" \
     -d '{"message": "test", "session_id": "test", "ragflow_config": {...}}'
   ```

### Performance Issues?
```bash
# Monitor performance
./manage.sh monitor

# Check resource usage
docker stats

# View performance logs
tail -f python-ragflow-service/logs/ragflow_performance.log
```

## 📊 Health Monitoring

### Quick Health Check
```bash
curl http://localhost:8001/health/simple
# Should return: {"status": "ok"}
```

### Detailed Health Check
```bash
curl http://localhost:8001/health | jq
# Shows detailed service status, components, and metrics
```

### Continuous Monitoring
```bash
./manage.sh monitor
# Runs continuous health checks with real-time updates
```

## 🔄 Updates

To update your deployment:

```bash
# Pull latest changes (if using git)
git pull

# Update and rebuild services
./manage.sh update dev

# For production
./manage.sh update prod
```

## 🆘 Getting Help

### Check Logs
```bash
# All services
./manage.sh logs

# Specific service
./manage.sh logs python-ragflow-service

# Error logs only
tail -f python-ragflow-service/logs/ragflow_errors.log
```

### Debug Information
```bash
# Comprehensive debug check
./manage.sh debug

# Service status
./manage.sh status

# Health check
./manage.sh health
```

### Test Everything
```bash
# Basic tests
./manage.sh test

# Comprehensive tests with your RAGFlow instance
python python-ragflow-service/test_client.py --test all \
  --ragflow-api-key "your_key" \
  --ragflow-base-url "your_url" \
  --ragflow-chat-id "your_chat_id"
```

## 🎉 You're All Set!

Your JURBOT application now has enhanced RAGFlow capabilities with:
- ✅ Interactive citations
- ✅ Thinking process display  
- ✅ Document references
- ✅ Real-time streaming
- ✅ Production-ready deployment
- ✅ Comprehensive monitoring

Enjoy your enhanced AI chatbot experience! 🤖✨

---

For detailed documentation, see:
- [Deployment Guide](DEPLOYMENT_GUIDE.md)
- [Python Service README](python-ragflow-service/README.md)
- [Troubleshooting](DEPLOYMENT_GUIDE.md#troubleshooting)
